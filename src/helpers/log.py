#!/usr/bin/python
# -*- coding: utf-8 -*-
import os
import logging
from logging.handlers import RotatingFileHandler

'''
# Create a custom logger
logger = logging.getLogger('authservice')


# Garante que o diretório 'logs/' exista
os.makedirs('logs', exist_ok=True)

# Create handlers
c_handler = logging.StreamHandler()
f_handler = logging.FileHandler('logs/authservice_applicationv2.log')
c_handler.setLevel(logging.INFO)
f_handler.setLevel(logging.ERROR)

# Create formatters and add it to handlers
c_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
f_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
c_handler.setFormatter(c_format)
f_handler.setFormatter(f_format)

# Add handlers to the logger
logger.addHandler(c_handler)
logger.addHandler(f_handler)

def errorlog(log):
    logger.error(log)

'''


def get_logger(name: str, log_dir="logs", filename="app.log", max_bytes=2_000_000, backup_count=5):
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, filename)

    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)
    logger.propagate = False  # Evita logs duplicados

    if not logger.handlers:
        formatter = logging.Formatter('%(asctime)s %(levelname)s [%(name)s]: %(message)s')

        # Handler para arquivo com rotação
        file_handler = RotatingFileHandler(log_path, maxBytes=max_bytes, backupCount=backup_count)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Handler para console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # Ativa debug detalhado de urllib3/requests
        # Ativa logger do urllib3 (usado pelo requests)
        urllib3_logger = logging.getLogger("urllib3")
        urllib3_logger.setLevel(logging.DEBUG)
        urllib3_logger.addHandler(file_handler)  # ← importante!
        urllib3_logger.propagate = False

    return logger    

