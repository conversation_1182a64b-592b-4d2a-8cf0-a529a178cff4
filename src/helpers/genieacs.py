import os
from database import *
from dotenv import load_dotenv
import requests

from helpers.log import get_logger

logger = get_logger("genieacs")


class GenieACS:
  def __init__(self):
      load_dotenv()
        
      self.url = f"http://{os.getenv('ACS_IP')}:7557"

  def __enter__(self):
      return self

  def __exit__(self, exc_type, exc_val, exc_tb):
      pass
  

  def get_device_data(self, mac=None, username=None, projection=None):
    try:
        if(mac):
          # Normalizar o MAC address
          mac = mac.replace(':', '')
          normalized_mac = ':'.join(mac[i:i+2] for i in range(0, 12, 2)).lower()
          
          # Construir a query
          query = {
              "InternetGatewayDevice.LANDevice.1.LANEthernetInterfaceConfig.1.MACAddress": normalized_mac
          }
        elif(username):
          # Construir a query para username
          query = {
              "InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.2.Username": username
          }
        
        # Preparar os parâmetros da requisição
        params = {
            'query': json.dumps(query)
        }
        
        # Adicionar projection se fornecido
        if projection:
            if isinstance(projection, list):
                projection = ','.join(projection)
            params['projection'] = projection
        
        url = f"{self.url}/devices/"
        r = requests.get(url, params=params)
        r.raise_for_status()  # Levanta exceção para status 4xx/5xx
        
        response_data = r.json()
        
        if not response_data:
            return None
            
        return response_data[0]  # Retorna todo o objeto do dispositivo
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"HTTP error: {str(e)}")
    except Exception as e:
        raise Exception(f"Error getting device data: {str(e)}")

  def removeacs(self, mac):
    try:
      data = self.get_device_data(mac=mac, projection=["_id", "_tags"])
      
      if data.get("_id"):
        id = data['_id']
        tags = data.get('_tags', [])
      else:
        raise Exception("ONU not found on ACS")
      
      # remove apenas as onus que nao possuem a tag Reauth
      if(not 'Reauth' in tags):

        url = f"{self.url}/devices/{id}/tasks?timeout=10&connection_request"
          
        pload = {'name':'reboot'}
        r = requests.post(url, data=json.dumps(pload))
          
        url = f"{self.url}/devices/{id}"
        r = requests.delete(url)

        #em seguida remove todas as onus filtrando pelo username (caso existam)
        patrimonio = getpatrimonio_alocado(mac)
        if(patrimonio):
          username = patrimonio['login']
          data = self.get_device_data(username=username, projection=["_id", "_tags"])
          if data.get("_id"):
            id = data['_id']
              
            #envia um comando de reboot antes de excluir
            url = f"{self.url}/devices/{id}/tasks?timeout=10&connection_request"

            pload = {'name':'reboot'}
            r = requests.post(url, data=json.dumps(pload))

            url = f"{self.url}/devices/{id}"
            r = requests.delete(url)
            return True
        else:
          self.removetag(mac, 'Reauth')
      else:
        return True
    except Exception as e:
      raise Exception("Error removing ONU from ACS")   


  def addtag(self, mac, tag):
    try:
      data = self.get_device_data(mac=mac, projection=["_id"])
        
      if data.get("_id"):
        id = data['_id']
        url = f"{self.url}/devices/{id}/tags/{tag}"
        r = requests.post(url)
    except Exception as e:
      raise Exception("Error adding tag to ONU")


  def removetag(self, mac, tag):
    try:
      data = self.get_device_data(mac=mac, projection=["_id"])
        
      if data.get("_id"):
        id = data['_id']
        url = f"{self.url}/devices/{id}/tags/{tag}"
        r = requests.delete(url)    
    except Exception as e:
      raise Exception("Error removing tag from ONU")


  def changeinformperiod(self, mac, period):
    try:
      data = self.get_device_data(mac=mac, projection=["_id"])
        
      if data.get("_id"):
        id = data['_id']
        url = f"{self.url}/devices/{id}/tasks?timeout=10&connection_request"
        pload = {
              "name": "setParameterValues",
              "parameterValues": [
                  ["InternetGatewayDevice.ManagementServer.PeriodicInformEnable", "true"],  
                  ["InternetGatewayDevice.ManagementServer.PeriodicInformInterval", period]
              ]}
        r = requests.post(url, data=json.dumps(pload))
    except Exception as e:
      raise Exception("Error changing inform period")
    

  def changepppoevlanid(self, mac, vlan):
    try:
      data = self.get_device_data(mac=mac, projection=["_id"])
        
      if data.get("_id"):
        id = data['_id']
      else:
        raise Exception("ONU not found on ACS")

      url = f"{self.url}/devices/{id}/tasks?timeout=10&connection_request"
      pload = {
              "name": "setParameterValues",
              "parameterValues": [
                  ["InternetGatewayDevice.WANDevice.1.WANConnectionDevice.1.WANPPPConnection.2.X_ZTE-COM_VLANID", vlan]
              ]}
      r = requests.post(url, data=json.dumps(pload))
    except Exception as e:
      raise Exception("Error changing pppoe vlan id")
    

  def registered(self, mac):
    try:
      try:
        data = self.get_device_data(mac=mac, projection=["_id"])
        
        if data.get("_id"):
          return True
        else:
          raise Exception("ONU not found on ACS")

      except Exception as e:
        return False
    except Exception as e:
      raise Exception("Error checking if ONU is registered")


  def reboot(self, mac):
    try:
      data = self.get_device_data(mac=mac, projection=["_id"])
        
      if data.get("_id"):
        id = data['_id']
      else:
        raise Exception("ONU not found on ACS")

      url = f"{self.url}/devices/{id}/tasks?timeout=10&connection_request"
      pload = {'name':'reboot'}
      r = requests.post(url, data=json.dumps(pload))
    except Exception as e:
      raise Exception("Error rebooting ONU")
       