from ..helpers.ztemanager import ZTEManager

item = {
    'olt_ip': '*************',
    'chassi': 1,
    'slot': 1,
    'pon': 1,
    'serial': 'ZTEGD1EB3668',
    'type': 'F670L',
    'name': 'ivone.salao',
    'vlan_tr069': 10,
    'vlan_pppoe': 3312,
    'vlan_iptv_unicast': 224,
    'vlan_iptv_multicast': 2,
    'bridge': False
}

with ZTEManager(
        host='*************'
    ) as manager:
        # Configuração inicial
        manager.disable_paging()
        
        response = manager.provision(chassi=item['chassi'], slot=item['slot'], pon=item['pon'], serial=item['serial'], type=item['type'], name=item['name'], vlan_tr069=item['vlan_tr069'], vlan_pppoe=item['vlan_pppoe'], vlan_iptv_unicast=item['vlan_iptv_unicast'], vlan_iptv_multicast=item['vlan_iptv_multicast'])
        print(response)

        #response = manager.list_auth(1, 1, 1)
        #print(response)
        #manager.disconnect()
        #del manager


#manager = ZTEManager(host='*************')
#manager.disable_paging()
#response = manager.list_auth(1, 1, 1)
#print(response)
#manager.disconnect()
#del manager