from src.helpers.genieacs import GenieACS

item = {
    'mac': '8C:DC:02:9D:B5:D4',
    'username': 'test_user',
    'serial': 'FHTT11cbf340'}

with GenieACS() as manager:
    response = manager.registered(mac=item['mac'])
    del manager

    if not response:
        raise Exception("ONU não registrada no ACS")

    print(f"ONU {item['mac']} esta registrada no ACS.")    




#GenieACS().addtag(mac="8C:DC:02:9D:B5:D4", tag="TestTag")
#GenieACS().removetag(mac="8C:DC:02:9D:B5:D4", tag="TestTag")
#print(GenieACS().registered(mac="8C:DC:02:9D:B5:D4"))



