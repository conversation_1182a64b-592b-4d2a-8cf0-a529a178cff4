from src.database import *
from src.tasks import *

item = {
    "serial": "FHTT11CBF340",
    "placa": "11",
    "porta": "1",
    "olt_ip": "***********"
}

precondition = "len(getdevice(filters)) > 0"

'''
filters = [
            ("serial", "=", item['serial']),
            ("OR", [
                [("placa", "!=", item['placa'])],
                [("porta", "!=", item['porta'])],
                [("olt_ip", "!=", item['olt_ip'])],
            ])
]
'''

filters = [('serial', '=', 'FHTT11CBF340'), ('OR', [[('placa', '!=', 10)], [('porta', '!=', 1)], [('olt_ip', '!=', '***********')]]), ('olt_modelo', '=', 'FBT')]

context = {"filters": filters}
functions = {"getdevice": getdevice}
print(evaluate_precondition(precondition, context, functions))

'''
olts = monitors('ZTE')
for olt in olts:
    print(f"OLT: {olt['name']} - IP: {olt['ip']}")
'''    