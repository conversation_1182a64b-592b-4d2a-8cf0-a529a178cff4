#!/usr/bin/python
# -*- coding: utf-8 -*-
from dotenv import load_dotenv
import os
import time
from database import *
from multiprocessing import Process
import time
import sys
from datetime import datetime
from helpers.ztemanager import ZTEManager
import threading
import uuid
from tasks import create_provision
#from helpers.genieacs import *
#from worker_provision_queue import conn
#from tasks_provision_zte import *

load_dotenv()

scriptname = 'DISCOVERY_V2'

#regra de modelo/plano
def plan_rule(model, plan, username):
    try:
        '''
        Regras ZTE				
        ==========
        Cliente	    Plano	    ONU Nova	ONU Antiga	Acao
        Novo*	    50	        F660	    null	    Provisionar
        Antigo**	50	        F660	    F660	    Provisionar
        Todos	    >50 & <100	F660	    Todos	    Provisionar
        Todos	    >=100	    F670	    Todos	    Provisionar
        
        * uma alocacao
        ** mais de uma alocacao
        '''
        valid = None
        f660 = '660' in model and '6600' not in model
        f670 = '670' in model
        f6600 = '6600' in model

        if(not f660 and not f670 and not f6600):
            return None

        if(int(plan) > 50 and int(plan) < 100):
            if(f670 or f6600):
                valid = 'F660'
            if(f660):
                return None    

        if int(plan) >= 100:
            if(f660):
                valid = 'F670'
            if(f670 or f6600):
                return None    

        if valid == None:
            onus = get_onus_alocadas(username)
            #Novo
            if(len(onus) <= 1):
                if(f670):
                    valid = 'F660'
            #Antigo
            else:
                anterior = onus[0]['descricao']
                if(f670):
                    if '660' in anterior and not '6600' in anterior:
                        valid = 'F660'
                if(f660):
                    if '670' in anterior:
                        valid = 'F670'
                    elif not '660' in anterior:    
                        valid = 'FIBERHOME'

        return valid
    except:
        return False  

#verifica se a onu possue todos os dados para iniciar o provisionamento
def checkonu(onu):
	try:
		missing_params = []

		# Busca uma única vez os parâmetros ignorados para esta ONU
		ignored_params = get_ignored_params(
			serial=onu.get('serial'),
			username=onu.get('username')
		)

		# Se ignored_params é um dict vazio, significa que todos os campos são 0
		# e a ONU deve ser completamente ignorada - mas isso é tratado em check_list
		# Aqui tratamos apenas os parâmetros específicos ignorados

		# obrigatorio patrimonio
		if(not ('patrimonio' in onu and onu['patrimonio']) and
		not (ignored_params and 'patrimonio' in ignored_params)):
			missing_params.append('PATRIMONIO')

		# Libera apenas se tiver a alocacao do comodato e nao esteja na tabela ignore com o parametro comodato=1
		if(not('id_comodato' in onu and onu['id_comodato']) and
		not (ignored_params and 'comodato' in ignored_params)):
			missing_params.append('ALOCACAO COMODATO')

		# Libera apenas se tiver o modelo e nao esteja na tabela ignore com o parametro modelo=1
		if(not('modelo' in onu and onu['modelo']) and
		not (ignored_params and 'modelo' in ignored_params)):
				missing_params.append('MODELO')

		# obrigatorio username
		if(not('username' in onu and onu['username'])):
			missing_params.append('USERNAME')

		# obrigatorio mac
		if(not('mac' in onu and onu['mac'])):
			missing_params.append('MAC')

		# obrigatorio login ativo
		if(not('login_ativo' in onu and onu['login_ativo'] == 'S') and
		not (ignored_params and 'login_ativo' in ignored_params)):
			missing_params.append('LOGIN ATIVO')

		# obrigatorio pacote wifi para onus zte que nao estejam na lista para ignorar
		if('username' in onu and onu['username'] and 'ZTE' in onu['serial'].upper() and
		not (ignored_params and 'pacote_wifi' in ignored_params) and
		not(get_pacotewifi(onu['username']))):
			missing_params.append('PACOTE WIFI')

		# verifica se o modelo da onu e compativel com o plano.
		if('plano' in onu and 'modelo' in onu and 'username' in onu and onu['username'] and
		not (ignored_params and 'plano' in ignored_params)):
			pending = plan_rule(onu['modelo'], onu['plano'], onu['username'])
			if(pending):
				missing_params.append('ONU INCOMPATIVEL. NECESSARIO {}'.format(pending))

		return missing_params
		
	except Exception as e:
		raise

# Faz as checagens da lista de onus nao autorizadas
# e adiciona na fila de provisionamento caso atenda os requisitos
def check_list(onu):
    serial_upper = onu['serial'].upper()

    # Consulta se a onu esta alocada
    patrimonio = getpatrimonio_alocado(onu['serial'])
    if not patrimonio:
        _create_canceled_provision(onu, 'ONU sem patrimônio')
        print(f"[{scriptname}] ONU {serial_upper} sem patrimonio")
        return False

    # Atualiza dados da ONU com informações do patrimônio
    onu.update({
        'mac': patrimonio['mac'],
        'patrimonio': patrimonio['patrimonio'],
        'login_ativo': patrimonio['login_ativo'],
        'data_comodato': patrimonio['data_comodato'],
        'id_comodato': patrimonio['id_comodato'],
        'username': patrimonio['login'],
        'plano': patrimonio['plano'],
        'tr069': 1 if 'ZTE' in serial_upper else 0
    })

    # Verifica se a ONU deve ser completamente ignorada
    ignored_params = get_ignored_params(serial=serial_upper)
    if ignored_params is not None and len(ignored_params) == 0:
        print(f"[{scriptname}] ONU {onu['serial']} esta na lista para ignorar e nao sera autorizada")
        return False

    # Verifica se já existe provisionamento em andamento
    if pending_provision(onu['serial']):
        print(f"[{scriptname}] Provisionamento pendente para {onu['serial']}")
        return False

    # Verifica se a ONU atende os requisitos para provisionamento
    missing_params = checkonu(onu)
    if missing_params:
        _create_canceled_provision(onu, f"Faltando parametros: {missing_params}")
        print(f"[{scriptname}] {serial_upper} Faltando parametros {missing_params}")
        return False

    # Verifica se deve provisionar apenas ONU de teste específica
    onu_testes = os.getenv('ONU_TESTES')
    if onu_testes and onu_testes != serial_upper:
        return False

    # Provisiona a ONU
    print(f"[{scriptname}] ONU {serial_upper} sera provisionada")
    print(f"[{scriptname}] Dados da ONU {serial_upper}: {onu}")
    result = create_provision(onu)
    print(f"[{scriptname}] Jobs criados para a ONU {serial_upper}: {result}")
    return True

def _create_canceled_provision(onu, exc_info):
    """Função auxiliar para criar provisionamento cancelado"""
    current_provision = {
        'id': uuid.uuid4().hex,
        'serial': onu.get('serial'),
        'username': onu.get('username'),
        'source': onu.get('source'),
        'olt': onu.get('olt'),
        'olt_ip': onu.get('olt_ip'),
        'olt_model': 'ZTE',
        'slot': onu.get('placa'),
        'pon': onu.get('porta'),
        'model': onu.get('modelo'),
        'status': 'canceled',
        'exc_info': exc_info,
        'enqueued_at': datetime.now()
    }
    update_provision(current_provision)

# Processa as onus nao autorizadas
def provision_onus(unauthorized_onus):
    for unauth in unauthorized_onus:
        onu = {
            'olt': unauth["olt_name"],
            'olt_ip': unauth["olt_ip"],
            'olt_model': 'ZTE',
            'chassi' : unauth['chassi'],
            'slot': unauth['slot'],
            'pon': unauth['pon'],
            'placa': unauth['slot'],
            'porta': unauth['pon'],
            'serial': unauth['serial'],
            'onu_type': unauth['type'],
            'modelo': unauth['type'],
            
            'mac': None,
            'login': None,
            'patrimonio': None,
            'login_ativo': None,
            'data_comodato': None,
            'id_comodato': None,
            
            'data': datetime.now(),
            'source': scriptname,
            'status': 'WAITING',
        }
        check_list(onu)

# ----------------------------------------------------------------    

# Executa o discovery de ONUs não autorizadas no OLT
def getdiscovery(olt):
    with ZTEManager(
        host=olt['ip'],
        name=olt['name'],
    ) as manager:
        manager.disable_paging()
        unauthorized_onus = manager.list_unauth()
        
        if (len(unauthorized_onus) == 0):
            print(f"Nenhuma onu aguardando provisionamento em {olt['name']} ({olt['ip']})")
            return
        
        print(f"{len(unauthorized_onus)} onu(s) em {olt['name']} ({olt['ip']}) encaminhada(s) para o provisionamento")
        provision_onus(unauthorized_onus)
        
# ----------------------------------------------------------------        
# ----------------------------------------------------------------

if __name__ == '__main__':
    try:
        # lista de olts zte habilitadas na tabela monitors com o task_group definido
        olts = monitors('ZTE')

        proccess = []

        for olt in olts:
            # inicia um processo para cada olt
            p = Process(target=getdiscovery, args=(olt,))
            p.start()
            proccess.append(p)
    except Exception as e:
        print('Ocorreu um erro em __main__: '+str(e))

