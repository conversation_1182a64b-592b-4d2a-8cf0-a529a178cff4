import redis
from rq.queue import Queue
from dotenv import load_dotenv
import os

load_dotenv()

redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = os.getenv('REDIS_PORT', 6379)
redis_pass = os.getenv('REDIS_PASS', 'admin')

conn = redis.Redis(
    host=redis_host,
    port=redis_port,
    password=redis_pass)

# Fila usada nos seus jobs
queue_name = "olt-*************"
q = Queue(queue_name, connection=conn)

# Remove todos os jobs pendentes da fila
q.empty()